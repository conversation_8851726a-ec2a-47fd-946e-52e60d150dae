import React, { createContext, useEffect, useState } from "react";
import { useLocation } from "react-router-dom";

export const NavBarContext = createContext();
export const NavBarColorContext = createContext();

const NavProvider = ({ children }) => {
    const [navColor, setNavColor] = useState('white')

    const [navOpen, setNavOpen] = useState(false)

    const locate = useLocation().pathname
    useEffect(() => {
        if (locate == '/projects' || locate == '/agents') {
            setNavColor('black')
        } else {
            setNavColor('white')
        }
    }, [locate])

    return (
        <NavBarContext.Provider value={[navOpen, setNavOpen]}>
            <NavBarColorContext.Provider value={[navColor, setNavColor]}>
                {children}
            </NavBarColorContext.Provider>
        </NavBarContext.Provider>
    );
};

export default NavProvider;