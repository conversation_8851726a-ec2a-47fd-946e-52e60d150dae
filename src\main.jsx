import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.jsx'
import { BrowserRouter } from 'react-router-dom'
import Lenis from 'lenis'
import Stair from './components/comman/Stair.jsx'
import NavContext from './context/NavContext.jsx'
import NavProvider from './context/NavContext.jsx'

const lenis = new Lenis()

function raf(time) {
  lenis.raf(time)
  requestAnimationFrame(raf)
}

requestAnimationFrame(raf)
createRoot(document.getElementById('root')).render(
  <StrictMode>
    <BrowserRouter>
      <Stair>
        <NavProvider>

          <App />
        </NavProvider>
      </Stair>
    </BrowserRouter>
  </StrictMode>,
)
