import { useGS<PERSON> } from "@gsap/react";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/all";
import { useRef, useEffect } from "react";

const AgentCard = () => {
  const sectionRef = useRef(null);
  const leftTextRef1 = useRef(null);
  const rightTextRef1 = useRef(null);
  const leftTextRef2 = useRef(null);
  const rightTextRef2 = useRef(null);
  const card1Ref = useRef(null);
  const card2Ref = useRef(null);

  gsap.registerPlugin(ScrollTrigger);

  useGSAP(() => {
    // Fade-in animation for the cards
    gsap.from(".card", {
      opacity: 0,
      y: 100,
      duration: 1,
      ease: "power2.out",
      stagger: 0.3,
      scrollTrigger: {
        trigger: sectionRef.current,
        start: "top 80%",
      },
    });

    // Background transition
    

    // Infinite marquee effect
    const animateText = (element, dir = 1) => {
      gsap.to(element, {
        xPercent: dir * 300,
        duration: 8,
        ease: "linear",
        repeat: -1,
        modifiers: {
          xPercent: gsap.utils.wrap(-300, 300),
        },
      });
    };

    animateText(leftTextRef1.current, 1);
    animateText(rightTextRef1.current, -1);
    animateText(leftTextRef2.current, 1);
    animateText(rightTextRef2.current, -1);

    // Fade out first card as second card enters
    gsap.to(card1Ref.current, {
      opacity: 0,
      ease: "none",
      scrollTrigger: {
        trigger: card2Ref.current,
        start: "top bottom",   // when 2nd card just enters
        end: "top center",     // until it's halfway
        scrub: true,           // smooth fade with scroll
      },
    });
  });

  // Split text into characters for animation
  useEffect(() => {
    const splitText = (element) => {
      if (element) {
        const text = element.textContent;
        element.innerHTML = text
          .split("")
          .map((char) => `<span class="char">${char}</span>`)
          .join("");
      }
    };
    splitText(leftTextRef1.current);
    splitText(rightTextRef1.current.querySelector(".name"));
    splitText(leftTextRef2.current);
    splitText(rightTextRef2.current.querySelector(".name"));
  }, []);

  return (
    <div ref={sectionRef} className="relative w-full">
      {/* First Card */}
      <div
        ref={card1Ref}
        className="sticky top-0 h-screen flex items-center justify-center overflow-hidden"
      >
        {/* LEFT TEXT */}
        <h1
          ref={leftTextRef1}
          className="absolute left-0 top-1/2 -translate-y-1/2 text-[7vw] font-bold text-lime-400 leading-none whitespace-nowrap"
        >
          SÉBASTIE
        </h1>

        {/* CENTER IMAGE */}
        <div className="card relative z-10 w-[27%] h-[40vw] rounded-4xl overflow-hidden shadow-xl">
          <img
            src="https://k72.ca/uploads/teamMembers/SebR_640X960-640x960.jpg"
            alt="Agent"
            className="w-full h-full object-cover"
          />
        </div>

        {/* RIGHT TEXT */}
        <div>
          <h1
            ref={rightTextRef1}
            className="absolute right-[0%] top-[70%] -translate-y-1/2 text-[7vw] font-bold text-lime-400 leading-none whitespace-nowrap z-60"
          >
            <span className="name">ROY</span>{" "}
            <span className="text-black text-xl">
              irecteur de création adjoint
            </span>
          </h1>
        </div>
      </div>

      {/* Second Card */}
      <div
        ref={card2Ref}
        className="sticky top-0 h-screen flex items-center justify-center overflow-hidden"
      >
        {/* LEFT TEXT */}
        <h1
          ref={leftTextRef2}
          className="absolute left-0 top-1/2 -translate-y-1/2 text-[7vw] font-bold text-lime-400 leading-none whitespace-nowrap"
        >
          SÉBASTIE
        </h1>

        {/* CENTER IMAGE */}
        <div className="card relative z-10 w-[27%] h-[40vw] rounded-4xl overflow-hidden shadow-xl">
          <img
            src="https://k72.ca/uploads/teamMembers/SebR_640X960-640x960.jpg"
            alt="Agent"
            className="w-full h-full object-cover"
          />
        </div>

        {/* RIGHT TEXT */}
        <div>
          <h1
            ref={rightTextRef2}
            className="absolute right-[0%] top-[70%] -translate-y-1/2 text-[7vw] font-bold text-lime-400 leading-none whitespace-nowrap z-60"
          >
            <span className="name">ROY</span>{" "}
            <span className="text-black text-xl">
              irecteur de création adjoint
            </span>
          </h1>
        </div>
      </div>
    </div>
  );
};

export default AgentCard;
