import { useGS<PERSON> } from "@gsap/react";
import ProjectCard from "../components/Projects/ProjectCard";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/all";

const Project = () => {
  const project = [
    {
      image1:
        "https://k72.ca/uploads/caseStudies/PJC/Thumbnails/PJC_SiteK72_Thumbnail_1280x960-1280x960.jpg",
      image2:
        "https://k72.ca/uploads/caseStudies/WIDESCAPE/WS---K72.ca---Thumbnail-1280x960.jpg",
    },
    {
      image1:
        "https://k72.ca/uploads/caseStudies/OKA/OKA_thumbnail-1280x960.jpg",
      image2:
        "https://k72.ca/uploads/caseStudies/Opto/thumbnailimage_opto-1280x960.jpg",
    },
    {
      image1:
        "https://k72.ca/uploads/caseStudies/LAMAJEURE_-_Son_sur_mesure/chalaxeur-thumbnail_img-1280x960.jpg",
      image2:
        "https://k72.ca/uploads/caseStudies/SHELTON/thumbnailimage_shelton-1280x960.jpg",
    },
  ];
  gsap.registerPlugin(ScrollTrigger)
  useGSAP(()=>{
    gsap.from('.hero',{
      height:"100px",
      stagger:{
        amount:0.3,
      },
      scrollTrigger:{
        trigger:'.lol',
        // markers:true,
        start:'top 100%',
        end:'top -150%',
        scrub:1,
        
      },
    })
  })

  return (
    <div className="lg:p-4 p-2 mb-[100vh]">
      <div className=" pt-[45vh]">
        <h2 className="font-[font2] lg:text-[9.5vw] text-7xl uppercase">
          Projets
        </h2>
      </div>
      <div className="-lg:mt-20 lol">
        {project.map((elem, idx)=> {
          return (
            <div
              key={idx}
              className="hero w-full lg:h-[650px] mb-4 flex lg:flex-row flex-col lg:gap-4 gap-2"
            >
              <ProjectCard image1={elem.image1} image2={elem.image2} />
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default Project;
