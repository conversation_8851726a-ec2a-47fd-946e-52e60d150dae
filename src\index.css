@import "tailwindcss";

@font-face {
    font-family: font1;
    src: url(../public/Lausanne-300.woff2);
}
@font-face {
    font-family: font2;
    src: url(../public/Lausanne-500.woff2);
}

body{
    overflow-x: hidden;
}

.moveX{
    animation-name: moveAnimation;
    animation-duration: 10s;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
}

.moveLink{
    opacity: 0;
    transition: all ease 0.3s;
}

.link:hover .moveLink{
    opacity: 1;
}

@keyframes moveAnimation {
    from{
        transform: translateX(0);
    }
    to{
        transform: translateX(-100%);
    }
}