import { useGS<PERSON> } from "@gsap/react";
import gsap from "gsap";
import React, { useContext, useEffect, useRef } from "react";
import { Link } from "react-router-dom";
import { NavBarContext } from "../../context/NavContext";

const FullScreenNav = () => {
  const fullLinksRef = useRef(null);
  const {navOpen, setNavOpen } = useContext(NavBarContext);
  const fullScreenRef = useRef(null);

  useEffect(() => {
    if (navOpen) {
      document.body.classList.add("overflow-hidden");
    } else {
      document.body.classList.remove("overflow-hidden");
    }
    // Cleanup on unmount
    return () => document.body.classList.remove("overflow-hidden");
  }, [navOpen]);


  // --- OPEN ANIMATION ---
  function gsapAnimation() {
    const tl = gsap.timeline();

    tl.to(".fullscreennav", { display: "block" });

    // 1. Stairs expand
    tl.to(".SecondStaier", {
      delay: 0.2,
      height: "100%",
      stagger: { amount: -0.3 },
      ease: "power3.inOut",
    });

    // 2. Header (logo + X) fade in
    tl.to(".nav-header", { opacity: 1, y: 0, ease: "power3.out" }, "-=0.2");

    // 3. Links fade/rotate in
    tl.to(".link", {
      opacity: 1,
      rotateX: 0,
      stagger: { amount: 0.3 },
      ease: "power3.out",
    });
  }

  // --- CLOSE ANIMATION ---
  function gsapAnimationReverse() {
    const tl = gsap.timeline();

    // 1. Links fade out
    tl.to(".link", {
      opacity: 0,
      rotateX: 90,
      stagger: { amount: 0.1 },
      ease: "power3.in",
    });

    // 2. Header fade out
    tl.to(".nav-header", { opacity: 0, y: -20, ease: "power3.in" });

    // 3. Stairs collapse
    tl.to(".SecondStaier", {
      height: 0,
      stagger: { amount: 0.1 },
      ease: "power3.inOut",
    });

    tl.to(".fullscreennav", { display: "none" });
  }

  useGSAP(
    function () {
      if (navOpen) {
        gsapAnimation();
      } else {
        gsapAnimationReverse();
      }
    },
    [navOpen]
  );

  return (
    <>
      <div
        ref={fullScreenRef}
        className={`fullscreennav ${navOpen ? "block" : "hidden"
          } h-screen w-full font-[font2] relative z-[9999] `}
      >
        {/* Background Stairs */}
        <div className="h-screen w-full fixed top-0">
          <div className="h-full w-full flex">
            <div className="SecondStaier h-0 w-1/5 bg-black"></div>
            <div className="SecondStaier h-0 w-1/5 bg-black"></div>
            <div className="SecondStaier h-0 w-1/5 bg-black"></div>
            <div className="SecondStaier h-0 w-1/5 bg-black"></div>
            <div className="SecondStaier h-0 w-1/5 bg-black"></div>
          </div>
        </div>

        {/* Content */}
        <div ref={fullLinksRef} className="relative">
          {/* Header */}
          <div className="nav-header flex items-center justify-between p-3 opacity-0 -translate-y-5">
            {/* Logo */}
            <div className="w-32 text-white flex items-center">
              <Link to={"/"} className="w-full">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 103 44"
                  fill="white"
                  className="w-28 h-auto"
                >
                  <path d="M35.1441047,8.4486911 L58.6905011,8.4486911 L58.6905011,-1.3094819e-14 L35.1441047,-1.3094819e-14 L35.1441047,8.4486911 Z M20.0019577,0.000230366492 L8.83414254,25.3433089 L18.4876971,25.3433089 L29.5733875,0.000230366492 L20.0019577,0.000230366492 Z M72.5255345,0.000691099476 L72.5255345,8.44846073 L94.3991559,8.44846073 L94.3991559,16.8932356 L72.5275991,16.8932356 L72.5275991,19.5237906 L72.5255345,19.5237906 L72.5255345,43.9274346 L102.80937,43.9274346 L102.80937,35.4798953 L80.9357483,35.4798953 L80.9357483,25.3437696 L94.3996147,25.3428482 L94.3996147,16.8953089 L102.80937,16.8953089 L102.80937,0.000691099476 L72.5255345,0.000691099476 Z M-1.30398043e-14,43.9278953 L8.78642762,43.9278953 L8.78642762,0.0057591623 L-1.30398043e-14,0.0057591623 L-1.30398043e-14,43.9278953 Z M58.6849955,8.4486911 L43.1186904,43.9274346 L52.3166592,43.9274346 L67.9877996,8.4486911 L58.6849955,8.4486911 Z M18.4688864,25.3437696 L26.7045278,43.9278953 L36.2761871,43.9278953 L28.1676325,25.3375497 L18.4688864,25.3437696 Z"></path>
                </svg>
              </Link>
            </div>

            {/* Close Button (smaller & visible) */}
            <div
              onClick={() => setNavOpen(false)}
              className="h-10 w-10 flex items-center justify-center relative cursor-pointer"
            >
              <div className="h-8 w-1 -rotate-45 absolute bg-[#D3FD50]"></div>
              <div className="h-8 w-1 rotate-45 absolute bg-[#D3FD50]"></div>
            </div>
          </div>

          {/* Links */}
          <div className="AllLinks py-8">
            <div className="link opacity-0 rotate-x-90 origin-top border-y border-[#404040] text-white relative">
              <h1 className="text-center text-[8vw] uppercase leading-[0.8] pt-5">
                Projects
              </h1>
              <div className="moveLink flex absolute top-0 bg-[#D3FD50] text-black">
                <div className="moveX flex items-center">
                  <h2 className="text-center text-[8vw] uppercase leading-[0.8] pt-5 whitespace-nowrap">Pour Tour Voir</h2>
                  <img className=" w-60 h-24 object-cover shrink-0 rounded-full"
                    src="https://k72.ca/uploads/caseStudies/PJC/Thumbnails/PJC_SiteK72_Thumbnail_640x290-640x290.jpg"
                    alt=""
                  />
                  <h2 className="text-center text-[8vw] uppercase leading-[0.8] pt-5 whitespace-nowrap">Pour Tour Voir</h2>
                  <img className=" w-60 h-24 object-cover shrink-0 rounded-full"
                    src="https://k72.ca/uploads/caseStudies/WIDESCAPE/WS---K72.ca---MenuThumbnail-640x290.jpg"
                    alt=""
                  />
                </div>
                <div className="moveX flex items-center">
                  <h2 className="text-center text-[8vw] uppercase leading-[0.8] pt-5 whitespace-nowrap">Pour Tour Voir</h2>
                  <img className=" w-60 h-24 object-cover shrink-0 rounded-full"
                    src="https://k72.ca/uploads/caseStudies/PJC/Thumbnails/PJC_SiteK72_Thumbnail_640x290-640x290.jpg"
                    alt=""
                  />
                  <h2 className="text-center text-[8vw] uppercase leading-[0.8] pt-5 whitespace-nowrap">Pour Tour Voir</h2>
                  <img className=" w-60 h-24 object-cover shrink-0 rounded-full"
                    src="https://k72.ca/uploads/caseStudies/WIDESCAPE/WS---K72.ca---MenuThumbnail-640x290.jpg"
                    alt=""
                  />
                </div>
              </div>
            </div>

            <div className="link opacity-0 rotate-x-90 origin-top border-b border-[#404040] text-white relative">
              <h1 className="text-center text-[8vw] uppercase leading-[0.8] pt-5">
                About
              </h1>
              <div className="moveLink flex absolute top-0 bg-[#D3FD50] text-black">
                <div className="moveX flex items-center">
                  <h2 className="text-center text-[8vw] uppercase leading-[0.8] pt-5 whitespace-nowrap">Pour Tour Voir</h2>
                  <img className=" w-60 h-24 object-cover shrink-0 rounded-full"
                    src="https://k72.ca/uploads/caseStudies/PJC/Thumbnails/PJC_SiteK72_Thumbnail_640x290-640x290.jpg"
                    alt=""
                  />
                  <h2 className="text-center text-[8vw] uppercase leading-[0.8] pt-5 whitespace-nowrap">Pour Tour Voir</h2>
                  <img className=" w-60 h-24 object-cover shrink-0 rounded-full"
                    src="https://k72.ca/uploads/caseStudies/WIDESCAPE/WS---K72.ca---MenuThumbnail-640x290.jpg"
                    alt=""
                  />
                </div>
                <div className="moveX flex items-center">
                  <h2 className="text-center text-[8vw] uppercase leading-[0.8] pt-5 whitespace-nowrap">Pour Tour Voir</h2>
                  <img className=" w-60 h-24 object-cover shrink-0 rounded-full"
                    src="https://k72.ca/uploads/caseStudies/PJC/Thumbnails/PJC_SiteK72_Thumbnail_640x290-640x290.jpg"
                    alt=""
                  />
                  <h2 className="text-center text-[8vw] uppercase leading-[0.8] pt-5 whitespace-nowrap">Pour Tour Voir</h2>
                  <img className=" w-60 h-24 object-cover shrink-0 rounded-full"
                    src="https://k72.ca/uploads/caseStudies/WIDESCAPE/WS---K72.ca---MenuThumbnail-640x290.jpg"
                    alt=""
                  />
                </div>
              </div>
            </div>

            <div className="link opacity-0 rotate-x-90 origin-top border-b border-[#404040] text-white relative">
              <h1 className="text-center text-[8vw] uppercase leading-[0.8] pt-5">
                Contact
              </h1>
              <div className="moveLink flex absolute top-0 bg-[#D3FD50] text-black">
                <div className="moveX flex items-center">
                  <h2 className="text-center text-[8vw] uppercase leading-[0.8] pt-5 whitespace-nowrap">Pour Tour Voir</h2>
                  <img className=" w-60 h-24 object-cover shrink-0 rounded-full"
                    src="https://k72.ca/uploads/caseStudies/PJC/Thumbnails/PJC_SiteK72_Thumbnail_640x290-640x290.jpg"
                    alt=""
                  />
                  <h2 className="text-center text-[8vw] uppercase leading-[0.8] pt-5 whitespace-nowrap">Pour Tour Voir</h2>
                  <img className=" w-60 h-24 object-cover shrink-0 rounded-full"
                    src="https://k72.ca/uploads/caseStudies/WIDESCAPE/WS---K72.ca---MenuThumbnail-640x290.jpg"
                    alt=""
                  />
                </div>
                <div className="moveX flex items-center">
                  <h2 className="text-center text-[8vw] uppercase leading-[0.8] pt-5 whitespace-nowrap">Pour Tour Voir</h2>
                  <img className=" w-60 h-24 object-cover shrink-0 rounded-full"
                    src="https://k72.ca/uploads/caseStudies/PJC/Thumbnails/PJC_SiteK72_Thumbnail_640x290-640x290.jpg"
                    alt=""
                  />
                  <h2 className="text-center text-[8vw] uppercase leading-[0.8] pt-5 whitespace-nowrap">Pour Tour Voir</h2>
                  <img className=" w-60 h-24 object-cover shrink-0 rounded-full"
                    src="https://k72.ca/uploads/caseStudies/WIDESCAPE/WS---K72.ca---MenuThumbnail-640x290.jpg"
                    alt=""
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default FullScreenNav;
