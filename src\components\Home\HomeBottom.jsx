// import React from 'react'

import { Link } from "react-router-dom";

const HomeBottom = () => {
  return (
    <div className="flex justify-center items-center gap-4 mb-[23vw] font-[font2] md:mb-1">
      <Link to={"/projects"}>
        <button className="text-[6vw] border-5 border-white rounded-full leading-[5vw] pt-2 md:pt-5 pb-1 md:pb-0 text-white hover:text-[#D3FD50] hover:border-[#D3FD50] outline-none cursor-pointer transition-all px-3 md:px-12 uppercase pb-0">
          projects
        </button>
      </Link>
      <Link to={"/agents"}>
        <button className="text-[6vw] border-5 border-white rounded-full leading-[5.5vw] pt-2 md:pt-5 pb-1 md:pb-0 text-white hover:text-[#D3FD50] hover:border-[#D3FD50] outline-none cursor-pointer transition-all px-3 md:px-12 uppercase pb-0">
          Agents
        </button>
      </Link>
    </div>
  );
};

export default HomeBottom;
