import { useGS<PERSON> } from "@gsap/react";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/all";
import React, { useRef } from "react";
const AgentHero = () => {
    const ImageDivRef = useRef(null);
    const ImageSectionRef = useRef(null);
    const Images = [
        "https://k72.ca/uploads/teamMembers/Carl_480x640-480x640.jpg",
        "https://k72.ca/uploads/teamMembers/Olivier_480x640-480x640.jpg",
        "https://k72.ca/uploads/teamMembers/Lawrence_480x640-480x640.jpg",
        "https://k72.ca/uploads/teamMembers/HugoJoseph_480x640-480x640.jpg",
        "https://k72.ca/uploads/teamMembers/ChantalG_480x640-480x640.jpg",
        "https://k72.ca/uploads/teamMembers/MyleneS_480x640-480x640.jpg",
        "https://k72.ca/uploads/teamMembers/SophieA_480x640-480x640.jpg",
        "https://k72.ca/uploads/teamMembers/Claire_480x640-480x640.jpg",
        "https://k72.ca/uploads/teamMembers/Michele_480X640-480x640.jpg",
        "https://k72.ca/uploads/teamMembers/MEL_480X640-480x640.jpg",
        "https://k72.ca/uploads/teamMembers/CAMILLE_480X640_2-480x640.jpg",
        "https://k72.ca/uploads/teamMembers/MAXIME_480X640_2-480x640.jpg",
        "https://k72.ca/uploads/teamMembers/MEGGIE_480X640_2-480x640.jpg",
        "https://k72.ca/uploads/teamMembers/joel_480X640_3-480x640.jpg",
    ];
    gsap.registerPlugin(ScrollTrigger);
    useGSAP(() => {
        gsap.to(ImageDivRef.current, {
            scrollTrigger: {
                trigger: ImageDivRef.current,
                // markers: true,
                start: "top 17%",
                end: "bottom -70%",
                scrub: 1,
                pin: true,
                pinSpacing: true,
                pinReparent: true,
                pinType: "transform",
                anticipatePin: 1,
                invalidateOnRefresh: true,
                onUpdate: (elem) => {
                    let imagesIndex;
                    if (elem.progress < 1) {
                        imagesIndex = Math.floor(elem.progress * Images.length);
                    } else {
                        imagesIndex = Images.length - 1;
                    }
                    ImageSectionRef.current.src = Images[imagesIndex];
                },
            },
        });
    });
    return (
        <div className="overflow-hidden">
            <div
                ref={ImageDivRef}
                className="hidden md:block absolute top-[10vh] left-[10%] sm:left-[20%] md:left-[25vw] lg:left-[30vw] h-[40vw] sm:h-[30vw] md:h-[25vw] lg:h-[20vw] w-[40vw] sm:w-[40vw] md:w-[20vw] lg:w-[15vw] overflow-hidden rounded-4xl bg-amber-200"
            >
                <img
                    ref={ImageSectionRef}
                    className="h-full w-full object-cover"
                    src="https://k72.ca/uploads/teamMembers/Carl_480x640-480x640.jpg"
                    alt=""
                />
            </div>
            <div className="relative font-[font2]">
                <div className="mt-[30vh] sm:mt-[40vh] md:mt-[50vh]">
                    <h1 className="text-[18vw] sm:text-[25vw] md:text-[22vw] lg:text-[20vw] text-black uppercase text-center leading-[18vw] sm:leading-[22vw] md:leading-[20vw] lg:leading-[18vw]">
                        Soixan7e Douze
                    </h1>
                </div>
                <div className="pl-[6%] sm:pl-[20%] md:pl-[30%] lg:pl-[42%]">
                    <p className="text-lg sm:text-2xl md:text-3xl lg:text-4xl leading-tight">
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Notre
                        curiosité nourrit notre créativité. On reste humbles et on dit non
                        aux gros egos, même le vôtre. Une marque est vivante. Elle a des
                        valeurs, une personnalité, une histoire. Si on oublie ça, on peut
                        faire de bons chiffres à court terme, mais on la tue à long terme.
                        C’est pour ça qu’on s’engage à donner de la perspective, pour
                        bâtir des marques influentes.
                    </p>
                </div>
                <div className="bottom-section flex flex-col items-start mt-[20vw] sm:mt-[18vw] md:mt-[16vw] lg:mt-[14vw] px-[5%] sm:px-[8%] md:px-[10%] lg:px-[12vw] font-[font2]">
                    <div className="flex flex-col sm:flex-row gap-[5vw] sm:gap-[10vw] md:gap-[15vw] lg:gap-[20vw]">
                        <div>Experties</div>
                        <div>
                            <ul>
                                <li>Stratégie</li>
                                <li>Publicité</li>
                                <li>Branding</li>
                                <li>Design</li>
                                <li>Contenu</li>
                            </ul>
                        </div>
                    </div>
                    <div className="flex flex-col sm:flex-row items-start sm:items-center justify-evenly gap-4 sm:gap-6 md:gap-8 lg:gap-10 pt-10 sm:pt-12 md:pt-16 lg:pt-20">
                        <div>
                            <p className="text-sm sm:text-base md:text-lg lg:text-xl">
                                Nos projets_ naissent dans l’humilité, grandissent dans la
                                curiosité et vivent grâce à la créativité sous toutes ses
                                formes.
                            </p>
                        </div>
                        <div>
                            <p className="text-sm sm:text-base md:text-lg lg:text-xl">
                                Notre création_ bouillonne dans un environnement où le talent
                                a le goût d’exploser. Où on se sent libre d’être la meilleure
                                version de soi-même.
                            </p>
                        </div>
                        <div>
                            <p className="text-sm sm:text-base md:text-lg lg:text-xl">
                                Notre culture_ c’est l’ouverture aux autres. Point. Tout
                                l’équipage participe à bâtir une agence dont on est fiers.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default AgentHero