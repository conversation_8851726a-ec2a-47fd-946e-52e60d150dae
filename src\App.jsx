import React from "react";
import { Route, Routes } from "react-router-dom";
import Agents from "./pages/Agents";
import Home from "./pages/Home";
import Projects from "./pages/Project";
import Navbar from "./components/Navigation/Navbar";
import FullScreenNav from "./components/Navigation/FullScreenNav";


const App = () => {
 
  return (
    <>
    <Navbar/>
    <FullScreenNav/>
      <Routes >
        <Route path="/" element={<Home />} />
        <Route path="/projects" element={<Projects />} />
        <Route path="/agents" element={<Agents />} />
      </Routes>
    </>
  );
};

export default App;
